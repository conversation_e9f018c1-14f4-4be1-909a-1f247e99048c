# jCloud管理后台配置文件 - 开发环境
server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: jcloud-admin
  profiles:
    active: prod # 明确指定为开发环境
  # 禁用Spring Security自动配置（使用sa-token）
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: **************************************************************************************************************************************************************
        username: vimbox
        password: Vimbox123@
        # 简化连接池配置，减少资源占用
        initial-size: 5
        min-idle: 5
        max-active: 100
        max-wait: 5000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 120000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 30
        filters: stat,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
        keep-alive: true
        keep-alive-between-time-millis: 120000
        phy-timeout-millis: 300000
        # 开启连接泄露检测，快速发现问题
        remove-abandoned: true
        remove-abandoned-timeout: 180
        log-abandoned: true
      slave:
        url: ********************************************************************************************************************************************************
        username: vimbox
        password: Vimbox123@
        # 简化连接池配置，减少资源占用
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 5000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        max-evictable-idle-time-millis: 600000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 30
        filters: stat,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
        keep-alive: true
        keep-alive-between-time-millis: 120000
        phy-timeout-millis: 300000
        # 开启连接泄露检测，快速发现问题
        remove-abandoned: true
        remove-abandoned-timeout: 180
        log-abandoned: true

      # Druid 监控和统计配置
      webStatFilter:
        enabled: true
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      statViewServlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true # 开发环境可以允许重置统计
        login-username: admin
        login-password: druid123
        allow: 127.0.0.1,***********/24
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 500
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: true

  # Redis配置
  data:
    redis:
      host: *************
      port: 6379
      password: vimboxredis123@
      database: 10
      timeout: 10s
      connect-timeout: 5s
      lettuce:
        pool:
          max-active: 20
          max-wait: 5000ms
          max-idle: 10
          min-idle: 5
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30s

# MyBatis-Flex配置
mybatis-flex:
  type-aliases-package: com.jcloud.**.entity
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-statement-timeout: 20
    default-fetch-size: 100
    cache-enabled: false # 开发环境关闭二级缓存，便于调试
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple
    local-cache-scope: statement
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    shrink-whitespaces-in-sql: true

# sa-token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: false
  token-style: uuid
  is-log: true
  is-read-body: false
  token-prefix: Bearer

# jCloud配置
jcloud:
  captcha:
    enabled: true
    type: ALPHANUMERIC
    length: 4
    expire-time: 300
    case-sensitive: false
    image:
      width: 120
      height: 40
      font-name: Arial
      font-size: 25
      line-count: 5
      noise-count: 50
      background-color: "255,255,255"
      text-color-range: "0,0,0-100,100,100"
      line-color-range: "150,150,150-200,200,200"
      noise-color-range: "100,100,100-150,150,150"
    security:
      max-verify-attempts: 5
      lock-time: 600
      enable-brute-force-protection: true
  cache:
    enabled: false # 开发环境关闭缓存，便于调试
    type: redis
    redis:
      time-to-live: 3600
      cache-null-values: false
      key-prefix: "jcloud:cache:"
      use-key-prefix: true
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=300s
    cache-names:
      - userCache
      - roleCache
      - permissionCache
      - tenantCache
      - menuCache
      - deptCache

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: jCloud权限管理系统API文档
    description: 企业级权限管理系统接口文档
    version: 1.0.0
    concat: <EMAIL>
    license: MIT
    license-url: https://opensource.org/licenses/MIT
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

  # SQL审计配置
  enabled: true
  slow-sql-threshold: 1000
  storage-types:
    - console # 开发环境只输出到控制台
  data-masking:
    enabled: false # 开发环境关闭数据脱敏，便于查看原始数据
    sensitive-fields:
      - password
      - pwd
      - passwd
      - phone
      - mobile
      - email
      - idcard
      - identity
      - bankcard
      - credit_card
  performance:
    monitor-enabled: true
    buffer-size: 1000
    max-wait-time: 5000

# 日志配置
logging:
  level:
    com.jcloud: info # 可按需改为 debug
    org.springframework.security: warn
    com.alibaba.druid: info
    com.alibaba.druid.pool: info
    com.alibaba.druid.pool.DruidDataSource: info
    mybatis-flex-sql: info
    druid.sql: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: ./logs/jcloud-admin.log
    max-size: 10MB # 开发环境文件大小可以设小一些
    max-history: 7 # 保留最近7天的日志

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,druid
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    druid:
      enabled: true
  health:
    db:
      enabled: true
    datasource:
      enabled: true